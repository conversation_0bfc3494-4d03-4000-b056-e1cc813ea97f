# 👔 Tailor Management System

A modern desktop application designed for tailors to manage customer records, suit orders, and billing efficiently by eliminating the hassle of paper-based work.

---

## 🧩 Features Overview

### 🔐 Login System
- Secure **Admin Login** with password reset functionality
- Logout confirmation with success and cancel options

### 👤 Customer Management
- Add new customers with complete measurement details
- View all customer records in a dynamic data table
- Search customers by **Name or ID**
- Update customer information
- Real-time form validation and error handling

### 📋 Suit Details & Order Management
- View **Pending** and **Ready** orders with counts
- Move orders between pending ↔ ready status
- All orders are searchable and editable dynamically

### 🧾 Receipt & Billing System
- Create and print suit-making **receipts** for pending orders
- Update suit details based on receipt
- Print **bills** for ready orders using customer search
- Once billed, orders are moved to **Delivered Orders**

### 📊 Dashboard
- Centralized navigation via a professional **navbar**
- Status indicators for total, pending, and delivered orders
- Fully styled interface with **animations** and transitions

### 💬 Notifications
- Success, error, and warning messages for all operations
- Smooth form controls and well-handled exceptions

---

## 🔧 Built With

- **C# (.NET Framework 4.8.2)**
- **Windows Forms (WinForms)**
- **SQL Server** (local database)
- Custom styling & dynamic control rendering

---

## 💡 Purpose

> This system is designed to help local tailors **digitally manage their customers, measurements, and suit orders**, replacing traditional pen-and-paper records. It provides a **modern, responsive, and efficient solution** to day-to-day tailoring operations.

---


## 📦 Future Plans

- Role-based login (admin, tailor, manager)
- Online order booking
- Customer portal and SMS notifications

---

## 🧑‍💻 Author

Developed by Muhammad Qasim Tanvir

